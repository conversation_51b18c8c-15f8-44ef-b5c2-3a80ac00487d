"""
Credit Chakra Trigger Engine v0.1
Sprint 1 - Day 3: Event Processing and Alert Generation

Processes raw events and generates alerts based on trigger rules:
- T-01: EMI_BOUNCE
- T-04: GST_DELAY  
- T-03: CASHFLOW_DIP
"""
import time
import threading
from datetime import datetime, timezone
from firebase_config import db
from typing import Dict, Any, Optional

class TriggerEngine:
    """Trigger Engine for processing raw events and generating alerts"""
    
    def __init__(self, polling_interval: int = 10):
        self.polling_interval = polling_interval
        self.running = False
        self.thread = None
        
    def generate_timestamp(self) -> str:
        """Generate ISO timestamp with timezone"""
        return datetime.now(timezone.utc).isoformat()
    
    def process_emi_bounce_event(self, event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process T-01: EMI_BOUNCE trigger"""
        payload = event.get("payload", {})
        
        alert = {
            "alert_id": f"alert_emi_bounce_{event['event_id']}",
            "msme_id": event["msme_id"],
            "trigger_id": "T-01",
            "trigger_type": "EMI_BOUNCE",
            "severity": "high",
            "title": "EMI Payment Bounce Detected",
            "description": f"EMI payment bounced for amount ₹{payload.get('emi_amount', 'N/A')}",
            "source_events": [event["event_id"]],
            "created_at": self.generate_timestamp(),
            "status": "active",
            "metadata": {
                "score_impact": -10,
                "bounce_reason": payload.get("bounce_reason", "unknown"),
                "emi_amount": payload.get("emi_amount"),
                "bounce_count": payload.get("bounce_count", 1)
            }
        }
        
        print(f"🚨 T-01 EMI_BOUNCE triggered for MSME: {event['msme_id']}")
        return alert
    
    def process_gst_delay_event(self, event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process T-04: GST_DELAY trigger"""
        payload = event.get("payload", {})
        months_missed = payload.get("months_missed", payload.get("missing_months", []))
        
        # Only trigger if 2 or more months missed
        if len(months_missed) >= 2:
            alert = {
                "alert_id": f"alert_gst_delay_{event['event_id']}",
                "msme_id": event["msme_id"],
                "trigger_id": "T-04",
                "trigger_type": "GST_DELAY",
                "severity": "medium",
                "title": "GST Filing Delay Detected",
                "description": f"GST filing missed for {len(months_missed)} consecutive months",
                "source_events": [event["event_id"]],
                "created_at": self.generate_timestamp(),
                "status": "active",
                "metadata": {
                    "score_impact": -5,
                    "months_missed": months_missed,
                    "last_filed": payload.get("last_filed"),
                    "consecutive_months": len(months_missed)
                }
            }
            
            print(f"🚨 T-04 GST_DELAY triggered for MSME: {event['msme_id']} ({len(months_missed)} months)")
            return alert
        
        print(f"⏭️ T-04 GST_DELAY not triggered - only {len(months_missed)} months missed")
        return None
    
    def process_cashflow_dip_event(self, event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process T-03: CASHFLOW_DIP trigger"""
        payload = event.get("payload", {})
        drop_pct = payload.get("drop_pct", 0)
        
        # Only trigger if drop is > 30%
        if drop_pct > 30:
            severity = "critical" if drop_pct > 50 else "high"
            score_impact = -15 if drop_pct > 50 else -7
            
            alert = {
                "alert_id": f"alert_cashflow_dip_{event['event_id']}",
                "msme_id": event["msme_id"],
                "trigger_id": "T-03",
                "trigger_type": "CASHFLOW_DIP",
                "severity": severity,
                "title": "Significant Cashflow Drop Detected",
                "description": f"Cash inflow dropped by {drop_pct}% from previous period",
                "source_events": [event["event_id"]],
                "created_at": self.generate_timestamp(),
                "status": "active",
                "metadata": {
                    "score_impact": score_impact,
                    "drop_percentage": drop_pct,
                    "previous_inflow": payload.get("previous_month_inflow"),
                    "current_inflow": payload.get("current_month_inflow")
                }
            }
            
            print(f"🚨 T-03 CASHFLOW_DIP triggered for MSME: {event['msme_id']} ({drop_pct}% drop)")
            return alert
        
        print(f"⏭️ T-03 CASHFLOW_DIP not triggered - only {drop_pct}% drop")
        return None
    
    def process_event(self, doc) -> bool:
        """Process a single event document"""
        try:
            event = doc.to_dict()
            event_type = event.get("type")
            event_id = event.get("event_id")
            msme_id = event.get("msme_id")
            
            print(f"🔍 Processing event: {event_id} (type: {event_type}, MSME: {msme_id})")
            
            alert = None
            
            # Apply trigger rules based on event type
            if event_type == "emi_bounce":
                alert = self.process_emi_bounce_event(event)
            elif event_type == "gst_delay":
                alert = self.process_gst_delay_event(event)
            elif event_type in ["inflow_drop", "cashflow_dip"]:
                alert = self.process_cashflow_dip_event(event)
            else:
                print(f"⚠️ No trigger rule for event type: {event_type}")
            
            # Use Firestore batch for atomic operations
            batch = db.batch()
            
            # Create alert if generated
            if alert:
                alert_ref = db.collection("alerts").document(alert["alert_id"])
                batch.set(alert_ref, alert)
                print(f"✅ Alert created: {alert['alert_id']}")
            
            # Mark event as processed
            event_ref = doc.reference
            batch.update(event_ref, {
                "status": "processed",
                "processed_at": self.generate_timestamp(),
                "processed_by": "trigger_engine_v0.1"
            })
            
            # Commit the batch
            batch.commit()
            print(f"✅ Event processed: {event_id}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to process event {doc.id}: {str(e)}")
            
            # Log failure to trigger_failures collection
            try:
                db.collection("trigger_failures").add({
                    "event_id": doc.id,
                    "error": str(e),
                    "raw_event": event if 'event' in locals() else None,
                    "created_at": self.generate_timestamp(),
                    "engine_version": "v0.1"
                })
                print(f"📝 Failure logged for event: {doc.id}")
            except Exception as log_error:
                print(f"❌ Failed to log error: {log_error}")
            
            return False
    
    def scan_and_process_events(self):
        """Scan for unprocessed events and process them"""
        try:
            # Query for unprocessed events
            query = db.collection("raw_events").where("status", "==", "unprocessed").limit(10)
            docs = query.stream()
            
            processed_count = 0
            for doc in docs:
                if self.process_event(doc):
                    processed_count += 1
            
            if processed_count > 0:
                print(f"📊 Processed {processed_count} events in this cycle")
            
        except Exception as e:
            print(f"❌ Engine scan error: {str(e)}")
    
    def run_engine(self):
        """Main engine loop"""
        print("🚀 Trigger Engine v0.1 started")
        print(f"📡 Polling every {self.polling_interval} seconds for unprocessed events...")
        print("🎯 Trigger rules active: T-01 (EMI_BOUNCE), T-04 (GST_DELAY), T-03 (CASHFLOW_DIP)")
        
        while self.running:
            self.scan_and_process_events()
            time.sleep(self.polling_interval)
        
        print("🛑 Trigger Engine stopped")
    
    def start(self):
        """Start the trigger engine in a background thread"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self.run_engine, daemon=True)
            self.thread.start()
            print("✅ Trigger Engine started in background")
        else:
            print("⚠️ Trigger Engine is already running")
    
    def stop(self):
        """Stop the trigger engine"""
        if self.running:
            self.running = False
            if self.thread:
                self.thread.join(timeout=5)
            print("🛑 Trigger Engine stopped")
        else:
            print("⚠️ Trigger Engine is not running")

# Global engine instance
trigger_engine = TriggerEngine(polling_interval=10)

def start_trigger_engine():
    """Start the trigger engine"""
    trigger_engine.start()

def stop_trigger_engine():
    """Stop the trigger engine"""
    trigger_engine.stop()

if __name__ == "__main__":
    # Run engine directly for testing
    engine = TriggerEngine(polling_interval=5)
    try:
        engine.running = True
        engine.run_engine()
    except KeyboardInterrupt:
        print("\n🛑 Engine stopped by user")
        engine.stop()
