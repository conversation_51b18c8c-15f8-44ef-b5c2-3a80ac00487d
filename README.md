# Credit Chakra Platform API

## Sprint 1 - Day 2: Backend Role Enforcement & Trigger Engine

A FastAPI-based credit assessment and monitoring platform for MSMEs using Firebase Firestore with role-based access control and automated trigger engine.

## 🏗️ Project Structure

```
credit-chakra-api/
├── main.py                 # FastAPI application with RBAC
├── firebase_config.py      # Firebase configuration
├── middleware.py           # Role-based access control middleware
├── trigger_engine.py       # Event processing trigger engine
├── requirements.txt        # Python dependencies
├── serviceAccountKey.json  # Firebase service account key
├── schemas/                # Pydantic schemas
│   ├── __init__.py
│   ├── msme.py            # MSME entity schema
│   ├── event.py           # Raw events schema
│   ├── alert.py           # Alerts schema
│   ├── score.py           # Chakra score schema
│   └── consent.py         # Consent ledger schema
├── routes/                 # API routes with RBAC
│   ├── __init__.py
│   ├── msmes.py           # MSME management routes
│   ├── events.py          # Event management routes
│   └── alerts.py          # Alert management routes
└── seed/                   # Test data seeding
    ├── __init__.py
    └── seed_data.py       # Seed functions
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the Application

```bash
uvicorn main:app --reload
```

The API will be available at: `http://localhost:8000`

### 3. Access Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 📊 Firestore Collections

The platform initializes the following collections with test data:

### 1. `msmes/` - MSME Entities
- Business information
- Contact details
- Current Chakra scores
- Partner/RM assignments

### 2. `raw_events/` - Incoming Events
- GST delays
- EMI bounces
- Cashflow dips
- Manual events

### 3. `alerts/` - Generated Alerts
- Trigger-based alerts
- Severity levels
- Status tracking
- Source event references

### 4. `chakra_score/` - Score History
- Score calculations
- Band assignments
- Factor breakdowns
- Version tracking

### 5. `consent_ledger/` - Data Permissions
- Consent tracking
- Data source permissions
- Expiration management
- Audit trail

## 🔧 API Endpoints

### Core Endpoints
- `GET /` - API information
- `GET /health` - Health check
- `GET /collections/stats` - Collection statistics

### Data Endpoints
- `GET /msmes` - List all MSMEs
- `GET /events` - List all raw events
- `GET /alerts` - List all alerts

### Admin Endpoints
- `POST /seed/reset` - Reset and re-seed test data

## 🧪 Test Data

The application automatically seeds test data on startup including:
- 2 sample MSMEs (Sharma Steel Works, Patel Textiles)
- Sample events (GST delay, EMI bounce)
- Generated alerts
- Score calculations
- Consent records

## 🔥 Firebase Configuration

The application uses Firebase Admin SDK with the service account key located at `./serviceAccountKey.json`.

Project ID: `credit-chakra-in`

## 📝 Next Steps

This Sprint 1 - Day 1 implementation provides:
- ✅ FastAPI application setup
- ✅ Firebase Firestore integration
- ✅ Complete schema definitions
- ✅ Test data seeding
- ✅ Basic CRUD endpoints
- ✅ API documentation

**Ready for Sprint 1 - Day 2**: Trigger Engine implementation!
