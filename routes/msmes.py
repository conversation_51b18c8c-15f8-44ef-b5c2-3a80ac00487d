"""
MSME routes with role-based access control
"""
from fastapi import APIRouter, Request, HTTPException, Depends
from typing import List, Dict, Any
from firebase_config import db
from middleware import get_role_and_scope, check_admin_access, check_msme_access, get_filtered_query_params
from schemas.msme import MSME

router = APIRouter(prefix="/msmes", tags=["MSMEs"])

@router.get("/", response_model=Dict[str, Any])
async def get_msmes(request: Request):
    """
    Get MSMEs based on user role and permissions
    - Admin: See all MSMEs
    - Partner: See MSMEs from their partner_id
    - RM: See MSMEs assigned to their rm_id
    - Viewer: See all MSMEs (read-only)
    """
    scope = get_role_and_scope(request)
    
    try:
        msmes_ref = db.collection("msmes")
        query = msmes_ref
        
        # Apply role-based filtering
        filters = get_filtered_query_params(scope)
        for field, value in filters.items():
            query = query.where(field, "==", value)
        
        docs = query.stream()
        msmes = []
        
        for doc in docs:
            msme_data = doc.to_dict()
            msme_data["id"] = doc.id
            
            # Double-check access for each MSME
            if check_msme_access(scope, msme_data):
                msmes.append(msme_data)
        
        return {
            "status": "success",
            "count": len(msmes),
            "data": msmes,
            "user_role": scope["role"],
            "filters_applied": filters
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{msme_id}", response_model=Dict[str, Any])
async def get_msme(msme_id: str, request: Request):
    """Get specific MSME by ID with role-based access control"""
    scope = get_role_and_scope(request)
    
    try:
        doc = db.collection("msmes").document(msme_id).get()
        
        if not doc.exists:
            raise HTTPException(status_code=404, detail="MSME not found")
        
        msme_data = doc.to_dict()
        msme_data["id"] = doc.id
        
        # Check access permissions
        if not check_msme_access(scope, msme_data):
            raise HTTPException(
                status_code=403, 
                detail="Access denied: You don't have permission to view this MSME"
            )
        
        return {
            "status": "success",
            "data": msme_data
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=Dict[str, Any])
async def create_msme(msme_data: MSME, request: Request):
    """Create new MSME (Admin only)"""
    scope = get_role_and_scope(request)
    check_admin_access(scope)
    
    try:
        msme_dict = msme_data.model_dump()
        
        # Check if MSME already exists
        existing_doc = db.collection("msmes").document(msme_dict["msme_id"]).get()
        if existing_doc.exists:
            raise HTTPException(
                status_code=409, 
                detail=f"MSME with ID {msme_dict['msme_id']} already exists"
            )
        
        # Create the MSME
        db.collection("msmes").document(msme_dict["msme_id"]).set(msme_dict)
        
        return {
            "status": "created",
            "msme_id": msme_dict["msme_id"],
            "message": "MSME created successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{msme_id}", response_model=Dict[str, Any])
async def update_msme(msme_id: str, msme_data: MSME, request: Request):
    """Update MSME (Admin only)"""
    scope = get_role_and_scope(request)
    check_admin_access(scope)
    
    try:
        # Check if MSME exists
        doc = db.collection("msmes").document(msme_id).get()
        if not doc.exists:
            raise HTTPException(status_code=404, detail="MSME not found")
        
        msme_dict = msme_data.model_dump()
        
        # Update the MSME
        db.collection("msmes").document(msme_id).update(msme_dict)
        
        return {
            "status": "updated",
            "msme_id": msme_id,
            "message": "MSME updated successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{msme_id}", response_model=Dict[str, Any])
async def delete_msme(msme_id: str, request: Request):
    """Delete MSME (Admin only)"""
    scope = get_role_and_scope(request)
    check_admin_access(scope)
    
    try:
        # Check if MSME exists
        doc = db.collection("msmes").document(msme_id).get()
        if not doc.exists:
            raise HTTPException(status_code=404, detail="MSME not found")
        
        # Delete the MSME
        db.collection("msmes").document(msme_id).delete()
        
        return {
            "status": "deleted",
            "msme_id": msme_id,
            "message": "MSME deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
