"""
Events routes with role-based access control
"""
from fastapi import APIRouter, Request, HTTPException
from typing import List, Dict, Any
from firebase_config import db
from middleware import get_role_and_scope, check_admin_access, check_msme_access
from schemas.event import RawEvent
from datetime import datetime

router = APIRouter(prefix="/events", tags=["Events"])

@router.get("/", response_model=Dict[str, Any])
async def get_events(request: Request, status: str = None, msme_id: str = None):
    """
    Get raw events based on user role and permissions
    - Admin: See all events
    - Partner/RM: See events for their MSMEs only
    - Viewer: See all events (read-only)
    """
    scope = get_role_and_scope(request)
    
    try:
        events_ref = db.collection("raw_events")
        query = events_ref
        
        # Apply status filter if provided
        if status:
            query = query.where("status", "==", status)
        
        # Apply MSME filter if provided
        if msme_id:
            query = query.where("msme_id", "==", msme_id)
        
        docs = query.stream()
        events = []
        
        for doc in docs:
            event_data = doc.to_dict()
            event_data["id"] = doc.id
            
            # Check if user has access to the MSME associated with this event
            if scope["role"] in ["admin", "viewer"]:
                events.append(event_data)
            else:
                # For partner/RM, check MSME access
                msme_doc = db.collection("msmes").document(event_data["msme_id"]).get()
                if msme_doc.exists:
                    msme_data = msme_doc.to_dict()
                    if check_msme_access(scope, msme_data):
                        events.append(event_data)
        
        return {
            "status": "success",
            "count": len(events),
            "data": events,
            "user_role": scope["role"],
            "filters": {
                "status": status,
                "msme_id": msme_id
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{event_id}", response_model=Dict[str, Any])
async def get_event(event_id: str, request: Request):
    """Get specific event by ID with role-based access control"""
    scope = get_role_and_scope(request)
    
    try:
        doc = db.collection("raw_events").document(event_id).get()
        
        if not doc.exists:
            raise HTTPException(status_code=404, detail="Event not found")
        
        event_data = doc.to_dict()
        event_data["id"] = doc.id
        
        # Check access permissions for the associated MSME
        if scope["role"] not in ["admin", "viewer"]:
            msme_doc = db.collection("msmes").document(event_data["msme_id"]).get()
            if msme_doc.exists:
                msme_data = msme_doc.to_dict()
                if not check_msme_access(scope, msme_data):
                    raise HTTPException(
                        status_code=403, 
                        detail="Access denied: You don't have permission to view this event"
                    )
            else:
                raise HTTPException(status_code=404, detail="Associated MSME not found")
        
        return {
            "status": "success",
            "data": event_data
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=Dict[str, Any])
async def create_event(event_data: RawEvent, request: Request):
    """Create new raw event (Admin only)"""
    scope = get_role_and_scope(request)
    check_admin_access(scope)
    
    try:
        event_dict = event_data.model_dump()
        
        # Validate that the MSME exists
        msme_doc = db.collection("msmes").document(event_dict["msme_id"]).get()
        if not msme_doc.exists:
            raise HTTPException(
                status_code=404, 
                detail=f"MSME with ID {event_dict['msme_id']} not found"
            )
        
        # Check if event already exists
        existing_doc = db.collection("raw_events").document(event_dict["event_id"]).get()
        if existing_doc.exists:
            raise HTTPException(
                status_code=409, 
                detail=f"Event with ID {event_dict['event_id']} already exists"
            )
        
        # Set default values if not provided
        if not event_dict.get("received_at"):
            event_dict["received_at"] = datetime.utcnow().isoformat() + "Z"
        
        if not event_dict.get("status"):
            event_dict["status"] = "unprocessed"
        
        # Create the event
        db.collection("raw_events").document(event_dict["event_id"]).set(event_dict)
        
        return {
            "status": "created",
            "event_id": event_dict["event_id"],
            "message": "Event created successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{event_id}/status", response_model=Dict[str, Any])
async def update_event_status(event_id: str, status: str, request: Request):
    """Update event status (Admin only)"""
    scope = get_role_and_scope(request)
    check_admin_access(scope)
    
    try:
        # Check if event exists
        doc = db.collection("raw_events").document(event_id).get()
        if not doc.exists:
            raise HTTPException(status_code=404, detail="Event not found")
        
        # Validate status
        valid_statuses = ["unprocessed", "processed", "failed", "ignored"]
        if status not in valid_statuses:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid status. Valid statuses: {valid_statuses}"
            )
        
        # Update the event status
        update_data = {
            "status": status,
            "processed_at": datetime.utcnow().isoformat() + "Z" if status == "processed" else None
        }
        
        db.collection("raw_events").document(event_id).update(update_data)
        
        return {
            "status": "updated",
            "event_id": event_id,
            "new_status": status,
            "message": "Event status updated successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
