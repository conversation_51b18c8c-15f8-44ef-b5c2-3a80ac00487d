"""
Alerts routes with role-based access control
"""
from fastapi import APIRouter, Request, HTTPException
from typing import List, Dict, Any
from firebase_config import db
from middleware import get_role_and_scope, check_admin_access, check_msme_access
from schemas.alert import Alert

router = APIRouter(prefix="/alerts", tags=["Alerts"])

@router.get("/", response_model=Dict[str, Any])
async def get_alerts(request: Request, status: str = None, severity: str = None, msme_id: str = None):
    """
    Get alerts based on user role and permissions
    - Admin: See all alerts
    - Partner/RM: See alerts for their MSMEs only
    - Viewer: See all alerts (read-only)
    """
    scope = get_role_and_scope(request)
    
    try:
        alerts_ref = db.collection("alerts")
        query = alerts_ref
        
        # Apply filters if provided
        if status:
            query = query.where("status", "==", status)
        if severity:
            query = query.where("severity", "==", severity)
        if msme_id:
            query = query.where("msme_id", "==", msme_id)
        
        docs = query.stream()
        alerts = []
        
        for doc in docs:
            alert_data = doc.to_dict()
            alert_data["id"] = doc.id
            
            # Check if user has access to the MSME associated with this alert
            if scope["role"] in ["admin", "viewer"]:
                alerts.append(alert_data)
            else:
                # For partner/RM, check MSME access
                msme_doc = db.collection("msmes").document(alert_data["msme_id"]).get()
                if msme_doc.exists:
                    msme_data = msme_doc.to_dict()
                    if check_msme_access(scope, msme_data):
                        alerts.append(alert_data)
        
        return {
            "status": "success",
            "count": len(alerts),
            "data": alerts,
            "user_role": scope["role"],
            "filters": {
                "status": status,
                "severity": severity,
                "msme_id": msme_id
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{alert_id}", response_model=Dict[str, Any])
async def get_alert(alert_id: str, request: Request):
    """Get specific alert by ID with role-based access control"""
    scope = get_role_and_scope(request)
    
    try:
        doc = db.collection("alerts").document(alert_id).get()
        
        if not doc.exists:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        alert_data = doc.to_dict()
        alert_data["id"] = doc.id
        
        # Check access permissions for the associated MSME
        if scope["role"] not in ["admin", "viewer"]:
            msme_doc = db.collection("msmes").document(alert_data["msme_id"]).get()
            if msme_doc.exists:
                msme_data = msme_doc.to_dict()
                if not check_msme_access(scope, msme_data):
                    raise HTTPException(
                        status_code=403, 
                        detail="Access denied: You don't have permission to view this alert"
                    )
            else:
                raise HTTPException(status_code=404, detail="Associated MSME not found")
        
        return {
            "status": "success",
            "data": alert_data
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{alert_id}/acknowledge", response_model=Dict[str, Any])
async def acknowledge_alert(alert_id: str, request: Request):
    """Acknowledge an alert (Admin, Partner, RM with access)"""
    scope = get_role_and_scope(request)
    
    try:
        # Check if alert exists
        doc = db.collection("alerts").document(alert_id).get()
        if not doc.exists:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        alert_data = doc.to_dict()
        
        # Check access permissions for the associated MSME
        if scope["role"] not in ["admin"]:
            msme_doc = db.collection("msmes").document(alert_data["msme_id"]).get()
            if msme_doc.exists:
                msme_data = msme_doc.to_dict()
                if not check_msme_access(scope, msme_data):
                    raise HTTPException(
                        status_code=403, 
                        detail="Access denied: You don't have permission to acknowledge this alert"
                    )
            else:
                raise HTTPException(status_code=404, detail="Associated MSME not found")
        
        # Update alert acknowledgment
        from datetime import datetime
        update_data = {
            "acknowledged_at": datetime.utcnow().isoformat() + "Z",
            "acknowledged_by": scope.get("user_id", f"{scope['role']}_user"),
            "status": "acknowledged"
        }
        
        db.collection("alerts").document(alert_id).update(update_data)
        
        return {
            "status": "acknowledged",
            "alert_id": alert_id,
            "acknowledged_by": update_data["acknowledged_by"],
            "message": "Alert acknowledged successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{alert_id}/resolve", response_model=Dict[str, Any])
async def resolve_alert(alert_id: str, request: Request):
    """Resolve an alert (Admin only)"""
    scope = get_role_and_scope(request)
    check_admin_access(scope)
    
    try:
        # Check if alert exists
        doc = db.collection("alerts").document(alert_id).get()
        if not doc.exists:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        # Update alert resolution
        from datetime import datetime
        update_data = {
            "resolved_at": datetime.utcnow().isoformat() + "Z",
            "status": "resolved"
        }
        
        db.collection("alerts").document(alert_id).update(update_data)
        
        return {
            "status": "resolved",
            "alert_id": alert_id,
            "message": "Alert resolved successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
