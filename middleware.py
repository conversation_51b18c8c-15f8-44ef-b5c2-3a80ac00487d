"""
Role-based access control middleware for Credit Chakra platform
"""
from fastapi import Request, HTTPException
from typing import Dict, Optional

def get_role_and_scope(request: Request) -> Dict[str, Optional[str]]:
    """
    Extract role and scope information from request headers
    Simulates authentication token parsing
    """
    role = request.headers.get("X-User-Role")
    partner_id = request.headers.get("X-Partner-Id")
    rm_id = request.headers.get("X-RM-Id")
    user_id = request.headers.get("X-User-Id")

    if not role:
        raise HTTPException(
            status_code=401, 
            detail="Missing role header. Please provide X-User-Role header."
        )

    # Validate role values
    valid_roles = ["admin", "partner", "rm", "viewer"]
    if role not in valid_roles:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid role '{role}'. Valid roles: {valid_roles}"
        )

    # Role-specific validation
    if role == "rm" and not rm_id:
        raise HTTPException(
            status_code=400,
            detail="RM role requires X-RM-Id header"
        )
    
    if role == "partner" and not partner_id:
        raise HTTPException(
            status_code=400,
            detail="Partner role requires X-Partner-Id header"
        )

    return {
        "role": role,
        "partner_id": partner_id,
        "rm_id": rm_id,
        "user_id": user_id
    }

def check_admin_access(scope: Dict[str, Optional[str]]) -> None:
    """Check if user has admin access"""
    if scope["role"] != "admin":
        raise HTTPException(
            status_code=403,
            detail="Admin access required for this operation"
        )

def check_write_access(scope: Dict[str, Optional[str]]) -> None:
    """Check if user has write access (admin or partner)"""
    if scope["role"] not in ["admin", "partner"]:
        raise HTTPException(
            status_code=403,
            detail="Write access requires admin or partner role"
        )

def check_msme_access(scope: Dict[str, Optional[str]], msme_data: Dict) -> bool:
    """
    Check if user has access to specific MSME based on role
    Returns True if access is allowed
    """
    role = scope["role"]
    
    if role == "admin":
        return True
    elif role == "partner":
        return msme_data.get("partner_id") == scope["partner_id"]
    elif role == "rm":
        return msme_data.get("rm_id") == scope["rm_id"]
    elif role == "viewer":
        # Viewers can see all data but cannot modify
        return True
    
    return False

def get_filtered_query_params(scope: Dict[str, Optional[str]]) -> Dict[str, str]:
    """
    Get query parameters for filtering data based on user role
    """
    role = scope["role"]
    filters = {}
    
    if role == "rm" and scope["rm_id"]:
        filters["rm_id"] = scope["rm_id"]
    elif role == "partner" and scope["partner_id"]:
        filters["partner_id"] = scope["partner_id"]
    
    return filters
