"""
Seed data functions for Credit Chakra platform
"""
from firebase_config import db
from datetime import datetime, timedelta
import uuid

def generate_timestamp(days_ago=0):
    """Generate ISO timestamp"""
    return (datetime.utcnow() - timedelta(days=days_ago)).isoformat() + "Z"

def seed_msme():
    """Seed MSME test data"""
    msmes = [
        {
            "msme_id": "MSME_00123",
            "name": "Sharma Steel Works",
            "gstin": "27ABCDE1234F1Z5",
            "business_type": "Manufacturing",
            "registered_phone": "+************",
            "status": "active",
            "onboarded_at": generate_timestamp(30),
            "partner_id": "NBFC_001",
            "rm_id": "RM_4432",
            "chakra_score": 67,
            "chakra_band": "green",
            "engagement_score": 54,
            "last_score_updated": generate_timestamp(1)
        },
        {
            "msme_id": "MSME_00124",
            "name": "Patel Textiles Ltd",
            "gstin": "24XYZAB5678G2H9",
            "business_type": "Textile",
            "registered_phone": "+************",
            "status": "active",
            "onboarded_at": generate_timestamp(45),
            "partner_id": "NBFC_002",
            "rm_id": "RM_4433",
            "chakra_score": 45,
            "chakra_band": "yellow",
            "engagement_score": 32,
            "last_score_updated": generate_timestamp(2)
        }
    ]
    
    for msme in msmes:
        db.collection("msmes").document(msme["msme_id"]).set(msme)
        print(f"✅ Seeded MSME: {msme['name']}")

def seed_raw_events():
    """Seed raw events test data"""
    events = [
        {
            "event_id": "event_12345",
            "msme_id": "MSME_00123",
            "source": "gst",
            "type": "gst_delay",
            "payload": {
                "missing_months": ["2025-03", "2025-04"],
                "last_filed": "2025-02"
            },
            "received_at": generate_timestamp(5),
            "ingested_by": "gst_adapter_v0.2",
            "status": "unprocessed"
        },
        {
            "event_id": "event_12346",
            "msme_id": "MSME_00124",
            "source": "banking",
            "type": "emi_bounce",
            "payload": {
                "emi_amount": 50000,
                "bounce_reason": "insufficient_funds",
                "account_number": "****1234"
            },
            "received_at": generate_timestamp(3),
            "ingested_by": "banking_adapter_v1.1",
            "status": "unprocessed"
        }
    ]
    
    for event in events:
        db.collection("raw_events").document(event["event_id"]).set(event)
        print(f"✅ Seeded Event: {event['event_id']} ({event['type']})")

def seed_alerts():
    """Seed alerts test data"""
    alerts = [
        {
            "alert_id": "alert_67890",
            "msme_id": "MSME_00123",
            "trigger_id": "T-04",
            "severity": "high",
            "title": "GST Filing Delay Detected",
            "description": "MSME has missed GST filing for 2 consecutive months",
            "source_events": ["event_12345"],
            "created_at": generate_timestamp(4),
            "status": "active",
            "metadata": {
                "missing_months": ["2025-03", "2025-04"],
                "impact_score": -15
            }
        }
    ]
    
    for alert in alerts:
        db.collection("alerts").document(alert["alert_id"]).set(alert)
        print(f"✅ Seeded Alert: {alert['title']}")

def seed_chakra_scores():
    """Seed Chakra scores test data"""
    scores = [
        {
            "score_id": "score_98765",
            "msme_id": "MSME_00123",
            "score": 67,
            "band": "green",
            "previous_score": 82,
            "score_change": -15,
            "calculated_at": generate_timestamp(1),
            "factors": {
                "gst_compliance": 0.7,
                "payment_history": 0.8,
                "cashflow_stability": 0.6,
                "engagement": 0.5
            },
            "alerts_considered": ["alert_67890"],
            "version": "v1.2"
        }
    ]
    
    for score in scores:
        db.collection("chakra_score").document(score["score_id"]).set(score)
        print(f"✅ Seeded Score: {score['msme_id']} - {score['score']}")

def seed_consent_ledger():
    """Seed consent ledger test data"""
    consents = [
        {
            "consent_id": "consent_11111",
            "msme_id": "MSME_00123",
            "data_source": "gst",
            "consent_type": "read",
            "granted_at": generate_timestamp(30),
            "granted_by": "user_admin",
            "expires_at": generate_timestamp(-335),  # Future date
            "status": "active",
            "purpose": "Credit assessment and monitoring",
            "scope": ["gst_returns", "gst_compliance", "filing_history"],
            "metadata": {
                "ip_address": "***********",
                "user_agent": "Mozilla/5.0 (Credit Chakra Platform)"
            }
        }
    ]
    
    for consent in consents:
        db.collection("consent_ledger").document(consent["consent_id"]).set(consent)
        print(f"✅ Seeded Consent: {consent['consent_id']}")

def seed_all():
    """Seed all test data"""
    print("🌱 Starting data seeding...")
    seed_msme()
    seed_raw_events()
    seed_alerts()
    seed_chakra_scores()
    seed_consent_ledger()
    print("✅ All test data seeded successfully!")

if __name__ == "__main__":
    seed_all()
