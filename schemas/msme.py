"""
MSME schema for Credit Chakra platform
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class MSME(BaseModel):
    """MSME entity schema"""
    msme_id: str = Field(..., description="Unique MSME identifier")
    name: str = Field(..., description="Business name")
    gstin: str = Field(..., description="GST identification number")
    business_type: str = Field(..., description="Type of business")
    registered_phone: str = Field(..., description="Registered phone number")
    status: str = Field(..., description="MSME status (active/inactive/suspended)")
    onboarded_at: str = Field(..., description="Onboarding timestamp")
    partner_id: str = Field(..., description="Partner/NBFC identifier")
    rm_id: str = Field(..., description="Relationship Manager identifier")
    chakra_score: int = Field(..., description="Current Chakra score")
    chakra_band: str = Field(..., description="Chakra band (red/yellow/green)")
    engagement_score: int = Field(..., description="Engagement score")
    last_score_updated: str = Field(..., description="Last score update timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "msme_id": "MSME_00123",
                "name": "Sharma Steel Works",
                "gstin": "27ABCDE1234F1Z5",
                "business_type": "Manufacturing",
                "registered_phone": "+919876543210",
                "status": "active",
                "onboarded_at": "2024-01-15T10:30:00Z",
                "partner_id": "NBFC_001",
                "rm_id": "RM_4432",
                "chakra_score": 67,
                "chakra_band": "green",
                "engagement_score": 54,
                "last_score_updated": "2024-01-15T10:30:00Z"
            }
        }
