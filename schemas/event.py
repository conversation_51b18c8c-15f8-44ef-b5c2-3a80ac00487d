"""
Event schema for Credit Chakra platform
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class RawEvent(BaseModel):
    """Raw event schema for incoming events"""
    event_id: str = Field(..., description="Unique event identifier")
    msme_id: str = Field(..., description="Associated MSME identifier")
    source: str = Field(..., description="Event source (gst/banking/manual)")
    type: str = Field(..., description="Event type (gst_delay/emi_bounce/cashflow_dip)")
    payload: Dict[str, Any] = Field(..., description="Event payload data")
    received_at: str = Field(..., description="Event received timestamp")
    ingested_by: str = Field(..., description="System component that ingested the event")
    status: str = Field(default="unprocessed", description="Processing status")
    processed_at: Optional[str] = Field(None, description="Processing timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "event_id": "event_12345",
                "msme_id": "MSME_00123",
                "source": "gst",
                "type": "gst_delay",
                "payload": {
                    "missing_months": ["2025-03", "2025-04"],
                    "last_filed": "2025-02"
                },
                "received_at": "2024-01-15T10:30:00Z",
                "ingested_by": "gst_adapter_v0.2",
                "status": "unprocessed"
            }
        }
