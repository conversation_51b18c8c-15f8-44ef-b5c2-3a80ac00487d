"""
Chakra Score schema for Credit Chakra platform
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

class ChakraScore(BaseModel):
    """Chakra Score schema"""
    score_id: str = Field(..., description="Unique score record identifier")
    msme_id: str = Field(..., description="Associated MSME identifier")
    score: int = Field(..., description="Current Chakra score (0-100)")
    band: str = Field(..., description="Score band (red/yellow/green)")
    previous_score: Optional[int] = Field(None, description="Previous score")
    score_change: Optional[int] = Field(None, description="Score change from previous")
    calculated_at: str = Field(..., description="Score calculation timestamp")
    factors: Dict[str, Any] = Field(..., description="Score calculation factors")
    alerts_considered: List[str] = Field(default=[], description="Alert IDs considered in calculation")
    version: str = Field(..., description="Scoring algorithm version")
    
    class Config:
        json_schema_extra = {
            "example": {
                "score_id": "score_98765",
                "msme_id": "MSME_00123",
                "score": 67,
                "band": "green",
                "previous_score": 82,
                "score_change": -15,
                "calculated_at": "2024-01-15T10:30:00Z",
                "factors": {
                    "gst_compliance": 0.7,
                    "payment_history": 0.8,
                    "cashflow_stability": 0.6,
                    "engagement": 0.5
                },
                "alerts_considered": ["alert_67890"],
                "version": "v1.2"
            }
        }
