"""
Consent schema for Credit Chakra platform
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

class ConsentLedger(BaseModel):
    """Consent ledger schema for tracking data permissions"""
    consent_id: str = Field(..., description="Unique consent identifier")
    msme_id: str = Field(..., description="Associated MSME identifier")
    data_source: str = Field(..., description="Data source (gst/banking/manual)")
    consent_type: str = Field(..., description="Type of consent (read/write/delete)")
    granted_at: str = Field(..., description="Consent granted timestamp")
    granted_by: str = Field(..., description="User who granted consent")
    expires_at: Optional[str] = Field(None, description="Consent expiration timestamp")
    revoked_at: Optional[str] = Field(None, description="Consent revocation timestamp")
    revoked_by: Optional[str] = Field(None, description="User who revoked consent")
    status: str = Field(default="active", description="Consent status")
    purpose: str = Field(..., description="Purpose of data access")
    scope: List[str] = Field(..., description="Data access scope")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    class Config:
        json_schema_extra = {
            "example": {
                "consent_id": "consent_11111",
                "msme_id": "MSME_00123",
                "data_source": "gst",
                "consent_type": "read",
                "granted_at": "2024-01-15T10:30:00Z",
                "granted_by": "user_admin",
                "expires_at": "2025-01-15T10:30:00Z",
                "status": "active",
                "purpose": "Credit assessment and monitoring",
                "scope": ["gst_returns", "gst_compliance", "filing_history"],
                "metadata": {
                    "ip_address": "***********",
                    "user_agent": "Mozilla/5.0..."
                }
            }
        }
