"""
Alert schema for Credit Chakra platform
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

class Alert(BaseModel):
    """Alert schema for generated alerts"""
    alert_id: str = Field(..., description="Unique alert identifier")
    msme_id: str = Field(..., description="Associated MSME identifier")
    trigger_id: str = Field(..., description="Trigger rule identifier")
    severity: str = Field(..., description="Alert severity (low/medium/high/critical)")
    title: str = Field(..., description="Alert title")
    description: str = Field(..., description="Alert description")
    source_events: List[str] = Field(..., description="List of source event IDs")
    created_at: str = Field(..., description="Alert creation timestamp")
    status: str = Field(default="active", description="Alert status")
    acknowledged_at: Optional[str] = Field(None, description="Acknowledgment timestamp")
    acknowledged_by: Optional[str] = Field(None, description="User who acknowledged")
    resolved_at: Optional[str] = Field(None, description="Resolution timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    class Config:
        json_schema_extra = {
            "example": {
                "alert_id": "alert_67890",
                "msme_id": "MSME_00123",
                "trigger_id": "T-04",
                "severity": "high",
                "title": "GST Filing Delay Detected",
                "description": "MSME has missed GST filing for 2 consecutive months",
                "source_events": ["event_12345"],
                "created_at": "2024-01-15T10:30:00Z",
                "status": "active",
                "metadata": {
                    "missing_months": ["2025-03", "2025-04"],
                    "impact_score": -15
                }
            }
        }
