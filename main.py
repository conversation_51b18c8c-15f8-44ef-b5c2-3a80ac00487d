"""
Credit Chakra Platform - FastAPI Application
Sprint 1 - Day 1: Firestore Collections Setup
"""
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from firebase_config import db
from seed.seed_data import seed_all
from schemas.msme import MSME
from schemas.event import RawEvent
from schemas.alert import <PERSON><PERSON>
from schemas.score import ChakraScore
from schemas.consent import ConsentLedger
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="Credit Chakra Platform API",
    description="Credit assessment and monitoring platform for MSMEs",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

@app.on_event("startup")
async def startup_event():
    """Initialize Firestore and seed test data on startup"""
    print("🚀 Starting Credit Chakra Platform...")
    print("📊 Initializing Firestore collections...")
    
    # Seed test data
    try:
        seed_all()
        print("✅ Firestore initialized with test data")
    except Exception as e:
        print(f"❌ Error seeding data: {e}")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Credit Chakra Platform API",
        "version": "1.0.0",
        "status": "running",
        "collections": [
            "msmes",
            "raw_events", 
            "alerts",
            "chakra_score",
            "consent_ledger"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test Firestore connection
        collections = db.collections()
        collection_names = [col.id for col in collections]
        
        return {
            "status": "healthy",
            "firestore": "connected",
            "collections": collection_names
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "unhealthy", "error": str(e)}
        )

@app.get("/collections/stats")
async def collection_stats():
    """Get statistics for all collections"""
    try:
        stats = {}
        collections = ["msmes", "raw_events", "alerts", "chakra_score", "consent_ledger"]
        
        for collection_name in collections:
            docs = db.collection(collection_name).stream()
            count = len(list(docs))
            stats[collection_name] = {"document_count": count}
        
        return {
            "status": "success",
            "collections": stats,
            "total_documents": sum(stat["document_count"] for stat in stats.values())
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/seed/reset")
async def reset_and_seed():
    """Reset and re-seed all test data"""
    try:
        # Clear existing data (optional - be careful in production)
        collections = ["msmes", "raw_events", "alerts", "chakra_score", "consent_ledger"]
        
        for collection_name in collections:
            docs = db.collection(collection_name).stream()
            for doc in docs:
                doc.reference.delete()
        
        # Re-seed data
        seed_all()
        
        return {
            "status": "success",
            "message": "All collections reset and re-seeded with test data"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/msmes")
async def get_msmes():
    """Get all MSMEs"""
    try:
        docs = db.collection("msmes").stream()
        msmes = []
        for doc in docs:
            msme_data = doc.to_dict()
            msme_data["id"] = doc.id
            msmes.append(msme_data)
        
        return {
            "status": "success",
            "count": len(msmes),
            "data": msmes
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/events")
async def get_events():
    """Get all raw events"""
    try:
        docs = db.collection("raw_events").stream()
        events = []
        for doc in docs:
            event_data = doc.to_dict()
            event_data["id"] = doc.id
            events.append(event_data)
        
        return {
            "status": "success",
            "count": len(events),
            "data": events
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/alerts")
async def get_alerts():
    """Get all alerts"""
    try:
        docs = db.collection("alerts").stream()
        alerts = []
        for doc in docs:
            alert_data = doc.to_dict()
            alert_data["id"] = doc.id
            alerts.append(alert_data)
        
        return {
            "status": "success",
            "count": len(alerts),
            "data": alerts
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
