"""
Credit Chakra Platform - FastAPI Application
Sprint 1 - Day 2: Backend Role Enforcement & Seed Simulation
"""
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from firebase_config import db
from seed.seed_data import seed_all
from routes import msmes, events, alerts
from trigger_engine import start_trigger_engine, stop_trigger_engine, trigger_engine
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="Credit Chakra Platform API",
    description="Credit assessment and monitoring platform for MSMEs with RBAC",
    version="1.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Include routers
app.include_router(msmes.router)
app.include_router(events.router)
app.include_router(alerts.router)

@app.on_event("startup")
async def startup_event():
    """Initialize Firestore and seed test data on startup"""
    print("🚀 Starting Credit Chakra Platform...")
    print("📊 Initializing Firestore collections...")

    # Seed test data
    try:
        seed_all()
        print("✅ Firestore initialized with test data")
    except Exception as e:
        print(f"❌ Error seeding data: {e}")

    # Start trigger engine
    try:
        start_trigger_engine()
        print("✅ Trigger Engine started")
    except Exception as e:
        print(f"❌ Error starting trigger engine: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    print("🛑 Shutting down Credit Chakra Platform...")
    try:
        stop_trigger_engine()
        print("✅ Trigger Engine stopped")
    except Exception as e:
        print(f"❌ Error stopping trigger engine: {e}")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Credit Chakra Platform API with RBAC",
        "version": "1.1.0",
        "status": "running",
        "features": [
            "Role-based access control",
            "MSME management",
            "Event processing",
            "Alert management"
        ],
        "collections": [
            "msmes",
            "raw_events",
            "alerts",
            "chakra_score",
            "consent_ledger"
        ],
        "roles": [
            "admin - Full access to all operations",
            "partner - Access to MSMEs from their partner_id",
            "rm - Access to MSMEs assigned to their rm_id",
            "viewer - Read-only access to all data"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test Firestore connection
        collections = db.collections()
        collection_names = [col.id for col in collections]

        return {
            "status": "healthy",
            "firestore": "connected",
            "collections": collection_names
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "unhealthy", "error": str(e)}
        )

@app.get("/collections/stats")
async def collection_stats():
    """Get statistics for all collections"""
    try:
        stats = {}
        collections = ["msmes", "raw_events", "alerts", "chakra_score", "consent_ledger"]

        for collection_name in collections:
            docs = db.collection(collection_name).stream()
            count = len(list(docs))
            stats[collection_name] = {"document_count": count}

        return {
            "status": "success",
            "collections": stats,
            "total_documents": sum(stat["document_count"] for stat in stats.values())
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/seed/reset")
async def reset_and_seed():
    """Reset and re-seed all test data"""
    try:
        # Clear existing data (optional - be careful in production)
        collections = ["msmes", "raw_events", "alerts", "chakra_score", "consent_ledger"]

        for collection_name in collections:
            docs = db.collection(collection_name).stream()
            for doc in docs:
                doc.reference.delete()

        # Re-seed data
        seed_all()

        return {
            "status": "success",
            "message": "All collections reset and re-seeded with test data"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/role-test")
async def role_test():
    """Test endpoint for role-based access control examples"""
    return {
        "message": "Role-based access control test examples",
        "examples": {
            "admin_access": {
                "description": "Admin can access all MSMEs",
                "curl": "curl -H 'X-User-Role: admin' http://localhost:8000/msmes/"
            },
            "rm_access": {
                "description": "RM can only see MSMEs assigned to them",
                "curl": "curl -H 'X-User-Role: rm' -H 'X-RM-Id: RM_001' http://localhost:8000/msmes/"
            },
            "partner_access": {
                "description": "Partner can only see MSMEs from their partner_id",
                "curl": "curl -H 'X-User-Role: partner' -H 'X-Partner-Id: NBFC_001' http://localhost:8000/msmes/"
            },
            "create_msme": {
                "description": "Only admin can create MSMEs",
                "curl": "curl -X POST -H 'X-User-Role: admin' -H 'Content-Type: application/json' -d '{...}' http://localhost:8000/msmes/"
            },
            "create_event": {
                "description": "Only admin can create events",
                "curl": "curl -X POST -H 'X-User-Role: admin' -H 'Content-Type: application/json' -d '{...}' http://localhost:8000/events/"
            }
        },
        "test_data": {
            "rm_ids": ["RM_001", "RM_002", "RM_4432", "RM_4433"],
            "partner_ids": ["NBFC_001", "NBFC_002"],
            "msme_ids": ["MSME_KAVITA", "MSME_PRECISION", "MSME_VINIT", "MSME_00123", "MSME_00124"]
        }
    }

@app.get("/trigger-engine/status")
async def trigger_engine_status():
    """Get trigger engine status"""
    return {
        "status": "running" if trigger_engine.running else "stopped",
        "polling_interval": trigger_engine.polling_interval,
        "version": "v0.1",
        "trigger_rules": [
            "T-01: EMI_BOUNCE - High severity alerts for payment bounces",
            "T-04: GST_DELAY - Medium severity alerts for 2+ months GST delays",
            "T-03: CASHFLOW_DIP - High/Critical alerts for 30%+ cashflow drops"
        ]
    }

@app.post("/trigger-engine/start")
async def start_trigger_engine_endpoint():
    """Start the trigger engine"""
    try:
        start_trigger_engine()
        return {"status": "started", "message": "Trigger engine started successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/trigger-engine/stop")
async def stop_trigger_engine_endpoint():
    """Stop the trigger engine"""
    try:
        stop_trigger_engine()
        return {"status": "stopped", "message": "Trigger engine stopped successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/trigger-engine/process-now")
async def process_events_now():
    """Manually trigger event processing"""
    try:
        trigger_engine.scan_and_process_events()
        return {"status": "processed", "message": "Manual event processing completed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Legacy endpoints (deprecated - use /msmes/, /events/, /alerts/ instead)
@app.get("/msmes-legacy", deprecated=True)
async def get_msmes_legacy():
    """Get all MSMEs (Legacy - use /msmes/ instead)"""
    try:
        docs = db.collection("msmes").stream()
        msmes = []
        for doc in docs:
            msme_data = doc.to_dict()
            msme_data["id"] = doc.id
            msmes.append(msme_data)

        return {
            "status": "success",
            "count": len(msmes),
            "data": msmes,
            "warning": "This endpoint is deprecated. Use /msmes/ with proper role headers instead."
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/events-legacy", deprecated=True)
async def get_events_legacy():
    """Get all raw events (Legacy - use /events/ instead)"""
    try:
        docs = db.collection("raw_events").stream()
        events = []
        for doc in docs:
            event_data = doc.to_dict()
            event_data["id"] = doc.id
            events.append(event_data)

        return {
            "status": "success",
            "count": len(events),
            "data": events,
            "warning": "This endpoint is deprecated. Use /events/ with proper role headers instead."
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/alerts-legacy", deprecated=True)
async def get_alerts_legacy():
    """Get all alerts (Legacy - use /alerts/ instead)"""
    try:
        docs = db.collection("alerts").stream()
        alerts = []
        for doc in docs:
            alert_data = doc.to_dict()
            alert_data["id"] = doc.id
            alerts.append(alert_data)

        return {
            "status": "success",
            "count": len(alerts),
            "data": alerts,
            "warning": "This endpoint is deprecated. Use /alerts/ with proper role headers instead."
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
